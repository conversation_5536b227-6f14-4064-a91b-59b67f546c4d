#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis Stream操作工具类

提供Redis Stream的生产者和消费者功能：
- Stream消息生产
- 消费者组管理
- 消息消费和确认
- 错误处理和重试机制

作者: User-DF Team
版本: 1.0.0
"""

import json
import time
import asyncio
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass, asdict
import redis.asyncio as redis
import logging

from ...core import Logger, ExceptionHandler, NetworkException, ErrorCode


@dataclass
class StreamConfig:
    """Stream配置"""
    stream_name: str
    max_length: Optional[int] = None  # Stream最大长度，None表示不限制
    approximate_max_length: bool = True  # 是否使用近似最大长度
    
    # 重试配置
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # 消息配置
    message_ttl: Optional[int] = None  # 消息TTL（秒）


@dataclass
class ConsumerGroupConfig:
    """消费者组配置"""
    group_name: str
    consumer_name: str
    start_id: str = "0"  # 开始消费的消息ID，"0"表示从头开始，"$"表示从最新开始
    
    # 消费配置
    block_time: int = 1000  # 阻塞等待时间（毫秒）
    count: int = 10  # 每次读取的消息数量
    
    # 重试配置
    pending_timeout: int = 300  # 待处理消息超时时间（秒）
    max_delivery_count: int = 3  # 最大投递次数


class RedisStreamProducer:
    """Redis Stream生产者"""
    
    def __init__(self, redis_client: redis.Redis, stream_config: StreamConfig):
        """
        初始化生产者
        
        Args:
            redis_client: Redis客户端
            stream_config: Stream配置
        """
        self.redis_client = redis_client
        self.config = stream_config
        self.logger = Logger.get_logger(f"RedisStreamProducer.{stream_config.stream_name}")
        
    async def send_message(self, data: Dict[str, Any], message_id: str = "*") -> str:
        """
        发送消息到Stream
        
        Args:
            data: 消息数据
            message_id: 消息ID，"*"表示自动生成
            
        Returns:
            消息ID
        """
        try:
            # 准备消息字段
            fields = {
                "data": json.dumps(data, ensure_ascii=False),
                "timestamp": str(int(time.time() * 1000)),
                "service": data.get("service", "unknown")
            }
            
            # 发送消息
            if self.config.max_length:
                # 限制Stream长度
                message_id = await self.redis_client.xadd(
                    self.config.stream_name,
                    fields,
                    id=message_id,
                    maxlen=self.config.max_length,
                    approximate=self.config.approximate_max_length
                )
            else:
                message_id = await self.redis_client.xadd(
                    self.config.stream_name,
                    fields,
                    id=message_id
                )
            
            self.logger.debug(f"消息已发送到Stream: {self.config.stream_name}, ID: {message_id}")
            return message_id
            
        except Exception as e:
            self.logger.error(f"发送消息到Stream失败: {e}")
            raise NetworkException(
                f"发送消息到Stream失败: {e}",
                ErrorCode.NETWORK_CONNECTION_ERROR
            )
    
    async def send_batch_messages(self, messages: List[Dict[str, Any]]) -> List[str]:
        """
        批量发送消息
        
        Args:
            messages: 消息列表
            
        Returns:
            消息ID列表
        """
        message_ids = []
        
        try:
            # 使用pipeline批量发送
            pipe = self.redis_client.pipeline()
            
            for message in messages:
                fields = {
                    "data": json.dumps(message, ensure_ascii=False),
                    "timestamp": str(int(time.time() * 1000)),
                    "service": message.get("service", "unknown")
                }
                
                if self.config.max_length:
                    pipe.xadd(
                        self.config.stream_name,
                        fields,
                        maxlen=self.config.max_length,
                        approximate=self.config.approximate_max_length
                    )
                else:
                    pipe.xadd(self.config.stream_name, fields)
            
            # 执行批量操作
            results = await pipe.execute()
            message_ids = [str(result) for result in results]
            
            self.logger.debug(f"批量发送 {len(messages)} 条消息到Stream: {self.config.stream_name}")
            return message_ids
            
        except Exception as e:
            self.logger.error(f"批量发送消息到Stream失败: {e}")
            raise NetworkException(
                f"批量发送消息到Stream失败: {e}",
                ErrorCode.NETWORK_CONNECTION_ERROR
            )
    
    async def get_stream_info(self) -> Dict[str, Any]:
        """获取Stream信息"""
        try:
            info = await self.redis_client.xinfo_stream(self.config.stream_name)
            return {
                "length": info.get("length", 0),
                "first_entry": info.get("first-entry"),
                "last_entry": info.get("last-entry"),
                "groups": info.get("groups", 0)
            }
        except Exception as e:
            self.logger.warning(f"获取Stream信息失败: {e}")
            return {}


class RedisStreamConsumer:
    """Redis Stream消费者"""
    
    def __init__(self, redis_client: redis.Redis, stream_config: StreamConfig, 
                 consumer_config: ConsumerGroupConfig):
        """
        初始化消费者
        
        Args:
            redis_client: Redis客户端
            stream_config: Stream配置
            consumer_config: 消费者配置
        """
        self.redis_client = redis_client
        self.stream_config = stream_config
        self.consumer_config = consumer_config
        self.logger = Logger.get_logger(
            f"RedisStreamConsumer.{stream_config.stream_name}.{consumer_config.group_name}"
        )
        
        self._is_running = False
        
    async def initialize(self):
        """初始化消费者组"""
        try:
            # 创建消费者组（如果不存在）
            await self.redis_client.xgroup_create(
                self.stream_config.stream_name,
                self.consumer_config.group_name,
                id=self.consumer_config.start_id,
                mkstream=True
            )
            self.logger.info(f"消费者组已创建: {self.consumer_config.group_name}")
            
        except redis.ResponseError as e:
            if "BUSYGROUP" in str(e):
                # 消费者组已存在
                self.logger.debug(f"消费者组已存在: {self.consumer_config.group_name}")
            else:
                self.logger.error(f"创建消费者组失败: {e}")
                raise
        except Exception as e:
            self.logger.error(f"初始化消费者组失败: {e}")
            raise NetworkException(
                f"初始化消费者组失败: {e}",
                ErrorCode.NETWORK_CONNECTION_ERROR
            )
    
    async def consume_messages(self, callback, auto_ack: bool = True) -> None:
        """
        消费消息

        Args:
            callback: 消息处理回调函数
            auto_ack: 是否自动确认消息
        """
        self._is_running = True
        self.logger.info(f"开始消费Stream: {self.stream_config.stream_name}")

        while self._is_running:
            try:
                # 首先处理待处理的消息
                await self._process_pending_messages(callback, auto_ack)

                # 读取新消息
                messages = await self.redis_client.xreadgroup(
                    self.consumer_config.group_name,
                    self.consumer_config.consumer_name,
                    {self.stream_config.stream_name: ">"},
                    count=self.consumer_config.count,
                    block=self.consumer_config.block_time
                )

                if messages:
                    await self._process_messages(messages[0][1], callback, auto_ack)
                else:
                    # 没有消息时短暂休眠，避免CPU占用过高
                    await asyncio.sleep(0.1)

            except asyncio.CancelledError:
                self.logger.info("消费任务被取消")
                break
            except redis.ConnectionError as e:
                self.logger.error(f"Redis连接错误: {e}")
                await asyncio.sleep(5)  # 连接错误时等待更长时间
            except Exception as e:
                self.logger.error(f"消费消息时发生错误: {e}")
                await asyncio.sleep(1)  # 错误时短暂休眠

        self.logger.info("消费循环已结束")
    
    async def _process_messages(self, messages: List[Tuple], callback, auto_ack: bool):
        """处理消息列表"""
        for message_id, fields in messages:
            try:
                # 解析消息数据
                data = json.loads(fields.get("data", "{}"))

                # 调用回调函数处理消息
                await callback(data, message_id, fields)

                # 自动确认消息
                if auto_ack:
                    await self.ack_message(message_id)

            except json.JSONDecodeError as e:
                self.logger.error(f"消息数据解析失败: {message_id}, {e}")
                # JSON解析失败，直接确认消息避免重复处理
                if auto_ack:
                    await self.ack_message(message_id)
            except Exception as e:
                self.logger.error(f"处理消息失败: {message_id}, {e}")
                # 处理失败时不确认消息，让Stream重试机制处理
                # 但要避免无限重试，可以根据投递次数决定是否确认
                if auto_ack:
                    # 这里可以添加更复杂的重试逻辑
                    # 暂时先确认消息，避免无限重试
                    await self.ack_message(message_id)
    
    async def _process_pending_messages(self, callback, auto_ack: bool):
        """处理待处理的消息"""
        try:
            # 获取待处理消息
            pending_messages = await self.redis_client.xpending_range(
                self.stream_config.stream_name,
                self.consumer_config.group_name,
                min="-",
                max="+",
                count=self.consumer_config.count
            )
            
            for pending_info in pending_messages:
                message_id = pending_info["message_id"]
                idle_time = pending_info["time_since_delivered"]
                delivery_count = pending_info["times_delivered"]
                
                # 检查是否超时或超过最大投递次数
                if (idle_time > self.consumer_config.pending_timeout * 1000 or 
                    delivery_count >= self.consumer_config.max_delivery_count):
                    
                    # 声明消息并重新处理
                    claimed_messages = await self.redis_client.xclaim(
                        self.stream_config.stream_name,
                        self.consumer_config.group_name,
                        self.consumer_config.consumer_name,
                        min_idle_time=0,
                        message_ids=[message_id]
                    )
                    
                    if claimed_messages:
                        await self._process_messages(claimed_messages, callback, auto_ack)
                        
        except Exception as e:
            self.logger.warning(f"处理待处理消息时发生错误: {e}")
    
    async def ack_message(self, message_id: str) -> bool:
        """
        确认消息
        
        Args:
            message_id: 消息ID
            
        Returns:
            是否确认成功
        """
        try:
            result = await self.redis_client.xack(
                self.stream_config.stream_name,
                self.consumer_config.group_name,
                message_id
            )
            
            if result:
                self.logger.debug(f"消息已确认: {message_id}")
            return bool(result)
            
        except Exception as e:
            self.logger.error(f"确认消息失败: {message_id}, {e}")
            return False
    
    async def stop(self):
        """停止消费"""
        self._is_running = False
        self.logger.info("消费者已停止")
    
    async def get_consumer_info(self) -> Dict[str, Any]:
        """获取消费者信息"""
        try:
            groups = await self.redis_client.xinfo_groups(self.stream_config.stream_name)
            for group in groups:
                if group["name"] == self.consumer_config.group_name:
                    consumers = await self.redis_client.xinfo_consumers(
                        self.stream_config.stream_name,
                        self.consumer_config.group_name
                    )
                    return {
                        "group_info": group,
                        "consumers": consumers
                    }
            return {}
        except Exception as e:
            self.logger.warning(f"获取消费者信息失败: {e}")
            return {}
