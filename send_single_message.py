#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发送单条测试消息
"""

import asyncio
import redis.asyncio as redis
import json
import time

async def send_single_message():
    """发送单条测试消息"""
    redis_client = redis.Redis(
        host="localhost",
        port=6379,
        db=0,
        decode_responses=True
    )
    
    try:
        await redis_client.ping()
        print("✅ Redis连接成功")
        
        stream_name = "mongodb_write_stream_dev"
        
        # 创建测试消息
        message_data = {
            "users": [{
                "_id": 9999999,
                "pid_list": ["test_new_pid_001", "test_new_pid_002"],
                "pid_count": 2,
                "updated_days": 20306,
                "prov_id": 100
            }],
            "created_at": time.time(),
            "test_message": True
        }
        
        # 发送消息
        message_id = await redis_client.xadd(
            stream_name,
            {"data": json.dumps(message_data)}
        )
        
        print(f"📤 发送测试消息: {message_id}")
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
    finally:
        await redis_client.aclose()

if __name__ == "__main__":
    asyncio.run(send_single_message())
