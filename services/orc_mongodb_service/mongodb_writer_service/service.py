#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB写入微服务

异步微服务，负责从消息队列接收数据并批量写入MongoDB
"""

import os
import sys
import asyncio
import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
import redis.asyncio as redis
from fastapi import FastAPI, HTTPException
import uvicorn

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import Config<PERSON><PERSON><PERSON>, <PERSON><PERSON>, ExceptionHandler
from shared.database.mongodb import MongoDBPool, MongoDBOperations
from shared.database.redis import RedisStreamConsumer, StreamConfig, ConsumerGroupConfig
from shared.utils import DataProcessor, TimeUtils


@dataclass
class WriteTask:
    """写入任务数据结构"""
    task_id: str
    user_data: List[Dict[str, Any]]  # 新格式：[{_id, pids, pid_count, updated_days, prov_id}]
    prov_id: int
    received_at: float
    status: str = "pending"
    retry_count: int = 0


class MongoDBWriterService:
    """MongoDB写入微服务"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        # 使用新的统一配置结构
        self.config = config_manager.get_config("mongodb_writer_service", default={})
        self.global_config = config_manager.get_config("", default={})  # 获取根配置
        self.logger = Logger.get_logger(__name__)
        self.exception_handler = ExceptionHandler
        
        # 初始化组件
        self.mongodb_pool = None
        self.redis_client = None
        self.stream_consumer = None
        
        # 服务状态
        self.is_running = False
        self.current_tasks = {}
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "total_users_written": 0,
            "total_inserts": 0,
            "total_updates": 0,
            "total_skips": 0,
            "start_time": time.time()
        }
        
        # 创建FastAPI应用
        self.app = FastAPI(title="MongoDB Writer Service", version="1.0.0")
        self._setup_routes()
        
        # 队列处理任务
        self.queue_task = None
    
    async def initialize(self):
        """初始化服务"""
        try:
            self.logger.info("初始化MongoDB写入微服务...")
            
            # 初始化MongoDB连接池
            self.mongodb_pool = MongoDBPool(config_manager=self.config_manager)
            await self.mongodb_pool.initialize()
            
            # 初始化Redis连接
            redis_config = self.global_config.get("redis", {})
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )

            # 测试Redis连接（异步方式）
            await self.redis_client.ping()

            # 初始化Redis Stream消费者
            stream_config_dict = redis_config.get("stream", {})
            consumer_config_dict = stream_config_dict.get("consumer_group", {})

            stream_config = StreamConfig(
                stream_name=stream_config_dict.get("stream_name", "mongodb_write_stream"),
                max_length=stream_config_dict.get("max_length"),
                approximate_max_length=stream_config_dict.get("approximate_max_length", True),
                max_retries=stream_config_dict.get("max_retries", 3),
                retry_delay=stream_config_dict.get("retry_delay", 1.0),
                message_ttl=stream_config_dict.get("message_ttl")
            )

            consumer_group_config = ConsumerGroupConfig(
                group_name=consumer_config_dict.get("group_name", "mongodb_writers"),
                consumer_name=consumer_config_dict.get("consumer_name", "writer_001"),
                start_id=consumer_config_dict.get("start_id", "0"),
                block_time=consumer_config_dict.get("block_time", 1000),
                count=consumer_config_dict.get("count", 10),
                pending_timeout=consumer_config_dict.get("pending_timeout", 300),
                max_delivery_count=consumer_config_dict.get("max_delivery_count", 3)
            )

            self.stream_consumer = RedisStreamConsumer(
                self.redis_client, stream_config, consumer_group_config
            )

            # 初始化消费者组
            await self.stream_consumer.initialize()

            self.is_running = True

            # 启动Stream处理任务
            self.queue_task = asyncio.create_task(self._process_stream())
            
            self.logger.info("MongoDB写入微服务初始化完成")
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭服务"""
        try:
            self.logger.info("关闭MongoDB写入微服务...")
            self.is_running = False

            # 停止Stream处理任务
            if self.queue_task and not self.queue_task.done():
                self.logger.info("停止Stream处理任务...")
                self.queue_task.cancel()
                try:
                    await asyncio.wait_for(self.queue_task, timeout=10.0)
                except (asyncio.CancelledError, asyncio.TimeoutError):
                    self.logger.info("Stream处理任务已停止")

            # 停止Stream消费者
            if self.stream_consumer:
                self.logger.info("停止Stream消费者...")
                await self.stream_consumer.stop()

            # 关闭Redis连接
            if self.redis_client:
                self.logger.info("关闭Redis连接...")
                await self.redis_client.aclose()

            # 关闭MongoDB连接
            if self.mongodb_pool:
                self.logger.info("关闭MongoDB连接...")
                self.mongodb_pool.close_all_connections()

            self.logger.info("MongoDB写入微服务已关闭")

        except Exception as e:
            self.logger.error(f"服务关闭失败: {e}")
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {
                "status": "healthy" if self.is_running else "unhealthy",
                "service": "mongodb_writer_service",
                "version": "1.0.0",
                "uptime": time.time() - self.stats["start_time"],
                "stats": self.stats
            }
        
        @self.app.get("/stats")
        async def get_stats():
            """获取统计信息"""
            return {
                "stats": self.stats,
                "current_tasks": len(self.current_tasks),
                "task_details": list(self.current_tasks.keys())
            }
        
        @self.app.get("/queue/status")
        async def get_queue_status():
            """获取Stream状态"""
            try:
                if self.stream_consumer:
                    stream_info = await self.stream_consumer.get_consumer_info()
                    stream_name = self.stream_consumer.stream_config.stream_name

                    return {
                        "stream_name": stream_name,
                        "stream_info": stream_info,
                        "is_processing": self.queue_task is not None and not self.queue_task.done()
                    }
                else:
                    return {"error": "Stream consumer not initialized"}
            except Exception as e:
                return {"error": str(e)}
    
    async def _process_stream(self):
        """处理Redis Stream中的消息"""
        self.logger.info(f"开始处理Stream: {self.stream_consumer.stream_config.stream_name}")

        # 定义消息处理回调函数
        async def message_callback(data: Dict[str, Any], message_id: str, fields: Dict[str, str]):
            """处理单个消息"""
            try:
                self.logger.debug(f"收到消息: {message_id}, 数据大小: {len(str(data))}")
                # 处理消息数据
                await self._process_single_message(data, message_id)
                self.logger.debug(f"消息处理完成: {message_id}")

            except Exception as e:
                self.logger.error(f"处理消息失败: {message_id}, {e}")
                # 消息处理失败时重新抛出异常，让Stream重试机制处理
                raise

        try:
            # 开始消费消息 - 这是一个持续运行的循环
            await self.stream_consumer.consume_messages(message_callback, auto_ack=True)
        except Exception as e:
            self.logger.error(f"Stream处理循环异常: {e}")
            # 如果Stream处理出现异常，服务应该继续运行，可以尝试重新连接
            if self.is_running:
                self.logger.info("尝试重新启动Stream处理...")
                await asyncio.sleep(5)  # 等待5秒后重试
                if self.is_running:  # 再次检查服务状态
                    await self._process_stream()  # 递归重启

    async def _process_single_message(self, data: Dict[str, Any], message_id: str):
        """处理单个消息"""
        try:
            # 从消息数据中提取用户数据
            user_data = data.get("users", data.get("user_data", []))
            if not user_data:
                self.logger.warning(f"消息数据为空，跳过: {message_id}")
                return

            # 获取省份ID
            prov_id = user_data[0].get("prov_id") if user_data else None
            if prov_id is None:
                self.logger.warning(f"无法获取省份ID，跳过消息: {message_id}")
                return

            # 创建写入任务 - 兼容旧的created_at字段
            received_time = data.get("created_at", time.time())  # 兼容旧消息
            write_task = WriteTask(
                task_id=message_id,
                user_data=user_data,
                prov_id=prov_id,
                received_at=received_time
            )

            # 执行写入操作
            await self._execute_write_task(write_task)

            self.logger.debug(f"消息处理完成: {message_id}, 用户数: {len(user_data)}")

        except Exception as e:
            self.logger.error(f"处理消息失败: {message_id}, {e}")
            raise
    
    async def _execute_write_task(self, write_task: WriteTask):
        """执行单个写入任务"""
        try:
            # 为该provid创建专门的MongoDB操作对象
            base_collection_name = self.global_config.get("mongodb", {}).get("collection", "user_pid_records_optimized")
            mongodb_ops = MongoDBOperations(
                self.mongodb_pool,
                base_collection_name,
                provid=write_task.prov_id
            )

            write_task.status = "processing"
            self.current_tasks[write_task.task_id] = write_task
            self.stats["total_tasks"] += 1

            # 执行写入操作
            result = await self._write_user_data(mongodb_ops, write_task.user_data)

            # 更新统计信息
            self.stats["completed_tasks"] += 1
            self.stats["total_users_written"] += len(write_task.user_data)
            self.stats["total_inserts"] += result.get("inserts", 0)
            self.stats["total_updates"] += result.get("updates", 0)
            self.stats["total_skips"] += result.get("skips", 0)

            write_task.status = "completed"

            # 清理完成的任务
            if write_task.task_id in self.current_tasks:
                del self.current_tasks[write_task.task_id]

            self.logger.debug(f"任务完成: {write_task.task_id}, provid: {write_task.prov_id}, 写入用户数: {len(write_task.user_data)}")

        except Exception as e:
            self.logger.error(f"任务写入失败: {write_task.task_id}, provid: {write_task.prov_id}, 错误: {e}")
            write_task.status = "failed"
            write_task.retry_count += 1
            self.stats["failed_tasks"] += 1

            # Stream会自动处理重试，这里只需要清理任务
            if write_task.task_id in self.current_tasks:
                del self.current_tasks[write_task.task_id]

            # 重新抛出异常，让Stream处理重试
            raise
    
    async def _write_user_data(self, mongodb_ops: MongoDBOperations, user_data: List[Dict[str, Any]]) -> Dict[str, int]:
        """写入用户数据到MongoDB"""
        try:
            inserts = 0
            updates = 0
            skips = 0

            batch_size = self.config.get("batch_processing", {}).get("write_batch_size", 1000)
            max_pids_per_user = self.config.get("max_pids_per_user", 300)

            for i in range(0, len(user_data), batch_size):
                batch_data = user_data[i:i + batch_size]

                # 处理每个用户的数据，确保符合新的数据结构
                processed_batch = []
                for user in batch_data:
                    # 确保数据结构正确
                    processed_user = {
                        "_id": user.get("_id") or user.get("uid"),
                        "pids": user.get("pids", user.get("pid_list", [])),
                        "pid_count": user.get("pid_count", len(user.get("pids", user.get("pid_list", [])))),
                        "updated_days": user.get("updated_days", int(time.time()) // 86400)
                    }

                    # 限制PID数量，按时间顺序保留最新的
                    if len(processed_user["pids"]) > max_pids_per_user:
                        processed_user["pids"] = processed_user["pids"][-max_pids_per_user:]
                        processed_user["pid_count"] = len(processed_user["pids"])

                    processed_batch.append(processed_user)

                # 执行批量upsert操作
                result = await mongodb_ops.bulk_upsert_async(processed_batch)

                inserts += result.get("inserted_count", 0)
                updates += result.get("modified_count", 0)
                skips += result.get("matched_count", 0) - result.get("modified_count", 0)

            return {
                "inserts": inserts,
                "updates": updates,
                "skips": skips
            }

        except Exception as e:
            self.logger.error(f"用户数据写入失败: {e}")
            raise
    

    
    def run(self, host: str = "0.0.0.0", port: int = 8002):
        """运行服务"""
        uvicorn.run(
            self.app,
            host=host,
            port=port,
            log_level="info"
        )


async def create_service():
    """创建服务实例"""
    config_manager = ConfigManager()
    service = MongoDBWriterService(config_manager)
    await service.initialize()
    return service


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="MongoDB写入微服务")
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")
    parser.add_argument("--port", type=int, default=8002, help="服务端口")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    # 设置配置文件路径
    if args.config:
        os.environ['USER_DF_CONFIG_FILE'] = args.config
    
    # 创建并运行服务
    async def main():
        service = await create_service()
        try:
            service.run(host=args.host, port=args.port)
        finally:
            await service.shutdown()
    
    asyncio.run(main())
