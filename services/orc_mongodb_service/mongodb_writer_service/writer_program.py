#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB写入程序
纯数据处理程序，负责从Redis Stream消费数据并写入MongoDB
不包含HTTP服务功能，专注于数据处理
"""

import os
import sys
import asyncio
import json
import time
import signal
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import redis.asyncio as redis

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import Config<PERSON><PERSON>ger, Logger, ExceptionHandler
from shared.database.mongodb import MongoDBPool, MongoDBOperations
from shared.database.redis import RedisStreamConsumer, StreamConfig, ConsumerGroupConfig
from shared.utils import DataProcessor, TimeUtils


@dataclass
class WriteTask:
    """写入任务数据结构"""
    task_id: str
    user_data: List[Dict[str, Any]]
    prov_id: int
    received_at: float
    status: str = "pending"
    retry_count: int = 0


class MongoDBWriterProgram:
    """MongoDB写入程序"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.config = config_manager.get_config("mongodb_writer_service", default={})
        self.global_config = config_manager.get_config("", default={})
        self.logger = Logger.get_logger(__name__)
        
        # 初始化组件
        self.mongodb_pool = None
        self.redis_client = None
        self.stream_consumer = None
        
        # 程序状态
        self.is_running = False
        self.shutdown_requested = False
        self.current_tasks = {}
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "total_users_written": 0,
            "total_inserts": 0,
            "total_updates": 0,
            "total_skips": 0,
            "start_time": time.time()
        }
        
        # 设置信号处理器
        self.setup_signal_handlers()
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，准备关闭程序...")
            self.shutdown_requested = True
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def initialize(self):
        """初始化程序"""
        try:
            self.logger.info("=== 初始化MongoDB写入程序 ===")
            self.logger.info(f"进程PID: {os.getpid()}")
            
            # 初始化MongoDB连接池
            self.mongodb_pool = MongoDBPool(config_manager=self.config_manager)
            await self.mongodb_pool.initialize()
            
            # 初始化Redis连接
            redis_config = self.global_config.get("redis", {})
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )
            
            # 测试Redis连接
            await self.redis_client.ping()
            
            # 初始化Redis Stream消费者
            stream_config_dict = redis_config.get("stream", {})
            consumer_config_dict = stream_config_dict.get("consumer_group", {})
            
            stream_config = StreamConfig(
                stream_name=stream_config_dict.get("stream_name", "mongodb_write_stream"),
                max_length=stream_config_dict.get("max_length"),
                approximate_max_length=stream_config_dict.get("approximate_max_length", True),
                max_retries=stream_config_dict.get("max_retries", 3),
                retry_delay=stream_config_dict.get("retry_delay", 1.0),
                message_ttl=stream_config_dict.get("message_ttl")
            )
            
            consumer_group_config = ConsumerGroupConfig(
                group_name=consumer_config_dict.get("group_name", "mongodb_writers"),
                consumer_name=consumer_config_dict.get("consumer_name", f"writer_{os.getpid()}"),
                start_id=consumer_config_dict.get("start_id", "0"),
                block_time=consumer_config_dict.get("block_time", 1000),
                count=consumer_config_dict.get("count", 10),
                pending_timeout=consumer_config_dict.get("pending_timeout", 300),
                max_delivery_count=consumer_config_dict.get("max_delivery_count", 3)
            )
            
            self.stream_consumer = RedisStreamConsumer(
                self.redis_client, stream_config, consumer_group_config
            )
            
            # 初始化消费者组
            await self.stream_consumer.initialize()
            
            self.is_running = True
            self.logger.info("=== MongoDB写入程序初始化完成 ===")
            
        except Exception as e:
            self.logger.error(f"程序初始化失败: {e}")
            raise
    
    async def run(self):
        """运行程序主循环"""
        try:
            self.logger.info("开始运行MongoDB写入程序...")
            
            # 定义消息处理回调函数
            async def message_callback(data: Dict[str, Any], message_id: str, fields: Dict[str, str]):
                """处理单个消息"""
                try:
                    await self._process_single_message(data, message_id)
                except Exception as e:
                    self.logger.error(f"处理消息失败: {message_id}, {e}")
                    raise
            
            # 启动消息处理循环
            while self.is_running and not self.shutdown_requested:
                try:
                    # 开始消费消息
                    await self.stream_consumer.consume_messages(message_callback, auto_ack=True)
                except Exception as e:
                    self.logger.error(f"消息消费循环异常: {e}")
                    if self.is_running and not self.shutdown_requested:
                        self.logger.info("等待5秒后重试...")
                        await asyncio.sleep(5)
            
            self.logger.info("程序主循环已结束")
            
        except Exception as e:
            self.logger.error(f"程序运行失败: {e}")
            raise
    
    async def _process_single_message(self, data: Dict[str, Any], message_id: str):
        """处理单个消息"""
        try:
            # 从消息数据中提取用户数据
            user_data = data.get("users", data.get("user_data", []))
            if not user_data:
                self.logger.warning(f"消息数据为空，跳过: {message_id}")
                return
            
            # 获取省份ID
            prov_id = user_data[0].get("prov_id") if user_data else None
            if prov_id is None:
                self.logger.warning(f"无法获取省份ID，跳过消息: {message_id}")
                return
            
            # 创建写入任务
            received_time = data.get("created_at", time.time())
            write_task = WriteTask(
                task_id=message_id,
                user_data=user_data,
                prov_id=prov_id,
                received_at=received_time
            )
            
            # 执行写入操作
            await self._execute_write_task(write_task)
            
        except Exception as e:
            self.logger.error(f"处理消息失败: {message_id}, {e}")
            raise
    
    async def _execute_write_task(self, write_task: WriteTask):
        """执行单个写入任务"""
        try:
            # 创建MongoDB操作对象
            base_collection_name = self.global_config.get("mongodb", {}).get("collection", "user_pid_records_optimized")
            mongodb_ops = MongoDBOperations(
                self.mongodb_pool,
                base_collection_name,
                provid=write_task.prov_id
            )
            
            write_task.status = "processing"
            self.current_tasks[write_task.task_id] = write_task
            self.stats["total_tasks"] += 1
            
            # 执行写入操作
            result = await self._write_user_data(mongodb_ops, write_task.user_data)
            
            # 更新统计信息
            self.stats["completed_tasks"] += 1
            self.stats["total_users_written"] += len(write_task.user_data)
            self.stats["total_inserts"] += result.get("inserts", 0)
            self.stats["total_updates"] += result.get("updates", 0)
            self.stats["total_skips"] += result.get("skips", 0)
            
            write_task.status = "completed"
            
            # 清理完成的任务
            if write_task.task_id in self.current_tasks:
                del self.current_tasks[write_task.task_id]
            
            # 定期输出统计信息
            if self.stats["completed_tasks"] % 10 == 0:
                self._log_stats()
            
        except Exception as e:
            self.logger.error(f"任务写入失败: {write_task.task_id}, provid: {write_task.prov_id}, 错误: {e}")
            write_task.status = "failed"
            write_task.retry_count += 1
            self.stats["failed_tasks"] += 1
            
            # 清理失败的任务
            if write_task.task_id in self.current_tasks:
                del self.current_tasks[write_task.task_id]
            
            raise
    
    async def _write_user_data(self, mongodb_ops: MongoDBOperations, user_data: List[Dict[str, Any]]) -> Dict[str, int]:
        """写入用户数据到MongoDB"""
        try:
            inserts = 0
            updates = 0
            skips = 0
            
            batch_size = self.config.get("batch_processing", {}).get("write_batch_size", 1000)
            max_pids_per_user = self.config.get("max_pids_per_user", 300)
            
            for i in range(0, len(user_data), batch_size):
                batch_data = user_data[i:i + batch_size]
                
                # 处理每个用户的数据
                processed_batch = []
                for user in batch_data:
                    processed_user = {
                        "_id": user.get("_id") or user.get("uid"),
                        "pids": user.get("pids", user.get("pid_list", [])),
                        "pid_count": user.get("pid_count", len(user.get("pids", user.get("pid_list", [])))),
                        "updated_days": user.get("updated_days", int(time.time()) // 86400)
                    }
                    
                    # 限制PID数量
                    if len(processed_user["pids"]) > max_pids_per_user:
                        processed_user["pids"] = processed_user["pids"][-max_pids_per_user:]
                        processed_user["pid_count"] = len(processed_user["pids"])
                    
                    processed_batch.append(processed_user)
                
                # 执行批量upsert操作
                result = await mongodb_ops.bulk_upsert_async(processed_batch)
                
                inserts += result.get("inserted_count", 0)
                updates += result.get("modified_count", 0)
                skips += result.get("matched_count", 0) - result.get("modified_count", 0)
            
            return {
                "inserts": inserts,
                "updates": updates,
                "skips": skips
            }
            
        except Exception as e:
            self.logger.error(f"用户数据写入失败: {e}")
            raise
    
    def _log_stats(self):
        """输出统计信息"""
        uptime = time.time() - self.stats["start_time"]
        self.logger.info(
            f"统计信息 - 运行时间: {uptime:.1f}s, "
            f"完成任务: {self.stats['completed_tasks']}, "
            f"失败任务: {self.stats['failed_tasks']}, "
            f"写入用户: {self.stats['total_users_written']}, "
            f"插入: {self.stats['total_inserts']}, "
            f"更新: {self.stats['total_updates']}, "
            f"跳过: {self.stats['total_skips']}"
        )
    
    async def shutdown(self):
        """关闭程序"""
        try:
            self.logger.info("=== 关闭MongoDB写入程序 ===")
            self.is_running = False
            
            # 停止Stream消费者
            if self.stream_consumer:
                await self.stream_consumer.stop()
            
            # 关闭Redis连接
            if self.redis_client:
                await self.redis_client.aclose()
            
            # 关闭MongoDB连接
            if self.mongodb_pool:
                self.mongodb_pool.close_all_connections()
            
            # 输出最终统计信息
            self._log_stats()
            self.logger.info("=== MongoDB写入程序已关闭 ===")
            
        except Exception as e:
            self.logger.error(f"程序关闭失败: {e}")


async def main():
    """主函数"""
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建程序实例
        program = MongoDBWriterProgram(config_manager)
        
        # 初始化程序
        await program.initialize()
        
        # 运行程序
        await program.run()
        
    except KeyboardInterrupt:
        print("\n接收到中断信号")
    except Exception as e:
        print(f"程序运行失败: {e}", file=sys.stderr)
        sys.exit(1)
    finally:
        if 'program' in locals():
            await program.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
