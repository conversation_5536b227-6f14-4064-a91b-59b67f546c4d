#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ORC MongoDB服务主入口
重构后的统一服务入口，使用nohup启动独立子服务并提供监控展示

功能：
1. 使用nohup启动独立的子服务进程
2. 监控各子服务状态和运行结果
3. 提供统一的服务管理和状态展示
4. 各子服务独立管理自己的日志文件
"""

import os
import sys
import asyncio
import argparse
import signal
import subprocess
import time
import json
import psutil
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from shared.core import ConfigManager, Logger
from services.orc_mongodb_service.monitoring_service.monitor import MonitoringService


@dataclass
class ServiceStatus:
    """服务状态"""
    name: str
    status: str
    pid: Optional[int] = None
    port: Optional[int] = None
    uptime: float = 0.0
    error_message: Optional[str] = None
    log_file: Optional[str] = None
    start_time: Optional[float] = None


@dataclass
class SubProcessInfo:
    """子进程信息"""
    name: str
    command: List[str]
    pid: Optional[int] = None
    process: Optional[subprocess.Popen] = None
    log_file: Optional[str] = None
    start_time: Optional[float] = None
    status: str = "stopped"


class ORCMongoDBMainService:
    """ORC MongoDB主服务 - 重构为进程管理和监控角色"""

    def __init__(self, config_path: Optional[str] = None):
        # 配置管理
        self.config_path = config_path or "configs/orc_mongodb_service/development.yaml"
        self.config_manager = ConfigManager(config_file=self.config_path)
        # 获取根配置（配置文件的所有内容）
        self.config = self.config_manager.get_config("", default={})
        self.logger = Logger.get_logger(__name__)

        # 服务状态
        self.is_running = False
        self.shutdown_requested = False

        # 子进程管理
        self.sub_processes: Dict[str, SubProcessInfo] = {}
        self.monitoring_service = None

        # 服务状态跟踪
        self.services_status = {}

        # 确保日志目录存在
        self._ensure_log_directories()

    def _ensure_log_directories(self):
        """确保日志目录存在"""
        try:
            log_config = self.config.get("logging", {})
            log_file = log_config.get("file_path", "logs/orc_mongodb_service/orc_mongodb_service.log")
            log_dir = Path(log_file).parent
            log_dir.mkdir(parents=True, exist_ok=True)

            # 为各子服务创建日志目录
            for service_name in ["mongodb_writer_service", "orc_processor_service"]:
                service_log_dir = log_dir / service_name
                service_log_dir.mkdir(parents=True, exist_ok=True)

        except Exception as e:
            print(f"创建日志目录失败: {e}")

    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，准备停止服务...")
            self.shutdown_requested = True

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def initialize(self):
        """初始化服务"""
        try:
            self.logger.info("=== 初始化ORC MongoDB主服务 ===")

            # 设置信号处理器
            self.setup_signal_handlers()

            # 准备子服务配置
            self._prepare_sub_services()

            # 启动所有子服务
            await self._start_all_sub_services()

            # 初始化监控服务
            await self._initialize_monitoring_service()

            self.is_running = True
            self.logger.info("=== ORC MongoDB主服务初始化完成 ===")

        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            await self.shutdown()
            raise
    
    def _prepare_sub_services(self):
        """准备子服务配置"""
        try:
            # 获取项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

            # MongoDB写入程序配置（改为程序模式）
            writer_log_file = f"logs/orc_mongodb_service/mongodb_writer_service/mongodb_writer_program_{int(time.time())}.log"
            writer_cmd = [
                sys.executable,
                "services/orc_mongodb_service/mongodb_writer_service/writer_program.py"
            ]

            self.sub_processes["mongodb_writer"] = SubProcessInfo(
                name="MongoDB写入程序",
                command=writer_cmd,
                log_file=writer_log_file
            )

            # ORC处理服务配置
            processor_log_file = f"logs/orc_mongodb_service/orc_processor_service/orc_processor_service_{int(time.time())}.log"
            processor_cmd = [
                sys.executable, "-m",
                "services.orc_mongodb_service.orc_processor_service.main",
                "--config", self.config_path
            ]

            self.sub_processes["orc_processor"] = SubProcessInfo(
                name="ORC处理服务",
                command=processor_cmd,
                log_file=processor_log_file
            )

            self.logger.info("子服务配置准备完成")

        except Exception as e:
            self.logger.error(f"准备子服务配置失败: {e}")
            raise
    
    async def _start_all_sub_services(self):
        """启动所有子服务"""
        try:
            self.logger.info("开始启动所有子服务...")

            # 设置环境变量
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            env = os.environ.copy()
            current_pythonpath = env.get('PYTHONPATH', '')
            if project_root not in current_pythonpath:
                env['PYTHONPATH'] = f"{project_root}:{current_pythonpath}" if current_pythonpath else project_root

            for service_key, service_info in self.sub_processes.items():
                await self._start_sub_service(service_key, service_info, env)

            self.logger.info("所有子服务启动完成")

        except Exception as e:
            self.logger.error(f"启动子服务失败: {e}")
            raise

    async def _start_sub_service(self, service_key: str, service_info: SubProcessInfo, env: Dict[str, str]):
        """使用nohup启动单个子服务"""
        try:
            self.logger.info(f"启动子服务: {service_info.name}")

            # 确保日志文件目录存在
            log_file_path = Path(service_info.log_file)
            log_file_path.parent.mkdir(parents=True, exist_ok=True)

            # 构建nohup命令
            nohup_cmd = ["nohup"] + service_info.command

            # 启动进程，将输出重定向到日志文件
            with open(service_info.log_file, 'w') as log_file:
                process = subprocess.Popen(
                    nohup_cmd,
                    stdout=log_file,
                    stderr=subprocess.STDOUT,
                    env=env,
                    preexec_fn=os.setsid  # 创建新的进程组，确保独立性
                )

            # 更新进程信息
            service_info.pid = process.pid
            service_info.process = process
            service_info.start_time = time.time()
            service_info.status = "starting"

            # 等待进程启动
            await asyncio.sleep(2)

            # 检查进程是否还在运行
            if process.poll() is None:
                service_info.status = "running"
                self.logger.info(f"{service_info.name} 启动成功 (PID: {process.pid}, 日志: {service_info.log_file})")

                # 更新服务状态
                self.services_status[service_key] = ServiceStatus(
                    name=service_info.name,
                    status="running",
                    pid=process.pid,
                    log_file=service_info.log_file,
                    start_time=service_info.start_time
                )
            else:
                service_info.status = "failed"
                raise Exception(f"{service_info.name} 启动失败")

        except Exception as e:
            self.logger.error(f"启动 {service_info.name} 失败: {e}")
            service_info.status = "failed"
            raise
    
    async def _initialize_monitoring_service(self):
        """初始化监控服务"""
        try:
            self.logger.info("初始化监控服务...")

            self.monitoring_service = MonitoringService(self.config_manager)
            await self.monitoring_service.initialize()

            self.services_status["monitoring"] = ServiceStatus(
                name="监控服务",
                status="running"
            )

            self.logger.info("监控服务初始化完成")

        except Exception as e:
            self.logger.error(f"监控服务初始化失败: {e}")
            raise
    
    async def check_sub_services_status(self):
        """检查所有子服务状态"""
        try:
            for service_key, service_info in self.sub_processes.items():
                if service_info.process:
                    # 检查进程是否还在运行
                    if service_info.process.poll() is None:
                        # 进程还在运行，更新状态
                        service_info.status = "running"
                        if service_key in self.services_status:
                            self.services_status[service_key].status = "running"
                    else:
                        # 进程已停止，检查是否需要重启
                        await self._handle_stopped_process(service_key, service_info)

        except Exception as e:
            self.logger.error(f"检查子服务状态失败: {e}")

    async def _handle_stopped_process(self, service_key: str, service_info: SubProcessInfo):
        """处理已停止的进程"""
        try:
            service_info.status = "stopped"
            if service_key in self.services_status:
                self.services_status[service_key].status = "stopped"
                self.services_status[service_key].error_message = "进程已停止"

            self.logger.warning(f"{service_info.name} 进程已停止 (PID: {service_info.pid})")

            # 检查是否需要自动重启
            restart_config = self.config.get("process_management", {}).get("auto_restart", {})
            if restart_config.get("enabled", False):
                max_restarts = restart_config.get("max_restarts", 3)
                restart_interval = restart_config.get("restart_interval", 30)

                # 检查重启次数限制
                restart_count = getattr(service_info, 'restart_count', 0)
                if restart_count < max_restarts:
                    self.logger.info(f"准备重启 {service_info.name} (重启次数: {restart_count + 1}/{max_restarts})")

                    # 等待重启间隔
                    await asyncio.sleep(restart_interval)

                    # 重启服务
                    await self._restart_sub_service(service_key, service_info)
                else:
                    self.logger.error(f"{service_info.name} 已达到最大重启次数 ({max_restarts})，停止自动重启")

        except Exception as e:
            self.logger.error(f"处理停止进程失败: {service_key}, {e}")

    async def _restart_sub_service(self, service_key: str, service_info: SubProcessInfo):
        """重启子服务"""
        try:
            self.logger.info(f"重启子服务: {service_info.name}")

            # 清理旧进程
            if service_info.process:
                try:
                    service_info.process.kill()
                    service_info.process.wait()
                except:
                    pass

            # 设置环境变量
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            env = os.environ.copy()
            current_pythonpath = env.get('PYTHONPATH', '')
            if project_root not in current_pythonpath:
                env['PYTHONPATH'] = f"{project_root}:{current_pythonpath}" if current_pythonpath else project_root

            # 重新启动服务
            await self._start_sub_service(service_key, service_info, env)

            # 增加重启计数
            service_info.restart_count = getattr(service_info, 'restart_count', 0) + 1

            self.logger.info(f"{service_info.name} 重启成功")

        except Exception as e:
            self.logger.error(f"重启子服务失败: {service_info.name}, {e}")
            service_info.status = "failed"


    
    async def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        # 先检查子服务状态
        await self.check_sub_services_status()

        status = {
            "main_service": {
                "is_running": self.is_running,
                "uptime": time.time() - getattr(self, 'start_time', time.time()),
                "role": "进程管理和监控"
            },
            "sub_processes": {},
            "services": {}
        }

        # 子进程状态
        for service_key, service_info in self.sub_processes.items():
            status["sub_processes"][service_key] = {
                "name": service_info.name,
                "status": service_info.status,
                "pid": service_info.pid,
                "log_file": service_info.log_file,
                "start_time": service_info.start_time,
                "uptime": time.time() - service_info.start_time if service_info.start_time else 0
            }

        # 服务状态
        for service_key, service_status in self.services_status.items():
            status["services"][service_key] = asdict(service_status)

        # 获取监控服务的详细状态
        if self.monitoring_service:
            try:
                monitoring_stats = await self.monitoring_service.collect_all_stats()
                status["monitoring_stats"] = monitoring_stats
            except Exception as e:
                self.logger.warning(f"获取监控统计失败: {e}")

        return status
    
    async def run_monitoring_loop(self):
        """运行监控循环"""
        self.logger.info("开始运行监控循环...")

        try:
            while self.is_running and not self.shutdown_requested:
                # 检查子服务状态
                await self.check_sub_services_status()

                # 显示状态摘要
                await self._display_status_summary()

                # 等待下次检查
                await asyncio.sleep(10)

        except KeyboardInterrupt:
            self.logger.info("接收到中断信号，停止监控循环")
        except Exception as e:
            self.logger.error(f"监控循环错误: {e}")

    async def _display_status_summary(self):
        """显示状态摘要"""
        try:
            status = await self.get_service_status()

            # 使用Rich库创建更好的显示效果
            from rich.console import Console
            from rich.table import Table
            from rich.panel import Panel
            from rich.text import Text

            console = Console()

            # 创建状态表格
            table = Table(title="ORC MongoDB服务状态", show_header=True, header_style="bold magenta")
            table.add_column("服务名称", style="cyan", no_wrap=True)
            table.add_column("状态", style="green")
            table.add_column("PID", style="yellow")
            table.add_column("运行时间", style="blue")
            table.add_column("日志文件", style="dim")

            # 主服务状态
            main_uptime = self._format_uptime(status['main_service']['uptime'])
            table.add_row(
                "主服务 (进程管理)",
                "[green]运行中[/green]" if status['main_service']['is_running'] else "[red]已停止[/red]",
                str(os.getpid()),
                main_uptime,
                "主进程日志"
            )

            # 子服务状态
            for service_key, service_info in status["sub_processes"].items():
                uptime = self._format_uptime(service_info['uptime'])
                status_color = self._get_status_color(service_info['status'])

                table.add_row(
                    service_info['name'],
                    f"[{status_color}]{service_info['status']}[/{status_color}]",
                    str(service_info['pid']) if service_info['pid'] else "N/A",
                    uptime,
                    str(service_info['log_file']) if service_info['log_file'] else "N/A"
                )

            # 显示表格
            console.print(table)

            # 显示额外信息
            if status.get("monitoring_stats"):
                monitoring_info = status["monitoring_stats"]
                queue_info = monitoring_info.get("queue", {})

                info_text = f"队列状态: {queue_info.get('status', 'unknown')} | "
                info_text += f"队列长度: {queue_info.get('queue_length', 0)} | "
                info_text += f"监控检查: {monitoring_info.get('monitor', {}).get('total_checks', 0)}"

                console.print(Panel(info_text, title="系统信息", style="dim"))

        except Exception as e:
            # 如果Rich库不可用，回退到简单日志
            self.logger.info("=== 服务状态摘要 ===")
            status = await self.get_service_status()
            self.logger.info(f"主服务运行时间: {status['main_service']['uptime']:.1f}秒")

            for service_key, service_info in status["sub_processes"].items():
                uptime = service_info['uptime']
                self.logger.info(f"{service_info['name']}: {service_info['status']} (PID: {service_info['pid']}, 运行时间: {uptime:.1f}秒)")
                if service_info.get('log_file'):
                    self.logger.info(f"  日志文件: {service_info['log_file']}")

    def _format_uptime(self, seconds: float) -> str:
        """格式化运行时间"""
        if seconds < 60:
            return f"{int(seconds)}秒"
        elif seconds < 3600:
            return f"{int(seconds // 60)}分{int(seconds % 60)}秒"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}时{minutes}分"

    def _get_status_color(self, status: str) -> str:
        """获取状态对应的颜色"""
        status_colors = {
            "running": "green",
            "starting": "yellow",
            "stopped": "red",
            "failed": "red",
            "unhealthy": "orange"
        }
        return status_colors.get(status, "white")

    async def restart_service(self, service_key: str):
        """手动重启指定服务"""
        try:
            if service_key not in self.sub_processes:
                raise ValueError(f"未知的服务: {service_key}")

            service_info = self.sub_processes[service_key]
            self.logger.info(f"手动重启服务: {service_info.name}")

            await self._restart_sub_service(service_key, service_info)

        except Exception as e:
            self.logger.error(f"手动重启服务失败: {service_key}, {e}")
            raise

    async def stop_service(self, service_key: str):
        """停止指定服务"""
        try:
            if service_key not in self.sub_processes:
                raise ValueError(f"未知的服务: {service_key}")

            service_info = self.sub_processes[service_key]
            self.logger.info(f"停止服务: {service_info.name}")

            if service_info.process and service_info.process.poll() is None:
                # 优雅关闭
                service_info.process.terminate()

                try:
                    service_info.process.wait(timeout=10)
                    self.logger.info(f"{service_info.name} 已优雅停止")
                except subprocess.TimeoutExpired:
                    # 强制关闭
                    service_info.process.kill()
                    service_info.process.wait()
                    self.logger.warning(f"{service_info.name} 被强制停止")

                service_info.status = "stopped"
                if service_key in self.services_status:
                    self.services_status[service_key].status = "stopped"

        except Exception as e:
            self.logger.error(f"停止服务失败: {service_key}, {e}")
            raise

    def get_process_info(self, service_key: str) -> Optional[Dict[str, Any]]:
        """获取进程详细信息"""
        try:
            if service_key not in self.sub_processes:
                return None

            service_info = self.sub_processes[service_key]

            if not service_info.process or not service_info.pid:
                return None

            # 使用psutil获取详细进程信息
            try:
                process = psutil.Process(service_info.pid)

                return {
                    "name": service_info.name,
                    "pid": service_info.pid,
                    "status": service_info.status,
                    "cpu_percent": process.cpu_percent(),
                    "memory_info": process.memory_info()._asdict(),
                    "create_time": process.create_time(),
                    "num_threads": process.num_threads(),
                    "log_file": service_info.log_file,
                    "restart_count": getattr(service_info, 'restart_count', 0)
                }
            except psutil.NoSuchProcess:
                return {
                    "name": service_info.name,
                    "pid": service_info.pid,
                    "status": "not_found",
                    "error": "进程不存在"
                }

        except Exception as e:
            self.logger.error(f"获取进程信息失败: {service_key}, {e}")
            return None

    async def shutdown(self):
        """关闭服务"""
        try:
            self.logger.info("=== 关闭ORC MongoDB主服务 ===")
            self.is_running = False

            # 关闭监控服务
            if self.monitoring_service:
                await self.monitoring_service.shutdown()

            # 关闭所有子进程
            await self._shutdown_all_sub_processes()

            self.logger.info("=== ORC MongoDB主服务已关闭 ===")

        except Exception as e:
            self.logger.error(f"服务关闭失败: {e}")

    async def _shutdown_all_sub_processes(self):
        """关闭所有子进程"""
        try:
            self.logger.info("关闭所有子进程...")

            for service_key, service_info in self.sub_processes.items():
                if service_info.process and service_info.process.poll() is None:
                    self.logger.info(f"关闭 {service_info.name} (PID: {service_info.pid})")

                    try:
                        # 首先尝试优雅关闭
                        service_info.process.terminate()

                        # 等待进程结束
                        try:
                            service_info.process.wait(timeout=10)
                            self.logger.info(f"{service_info.name} 已优雅关闭")
                        except subprocess.TimeoutExpired:
                            # 强制杀死进程
                            service_info.process.kill()
                            service_info.process.wait()
                            self.logger.warning(f"{service_info.name} 被强制关闭")

                        service_info.status = "stopped"

                    except Exception as e:
                        self.logger.error(f"关闭 {service_info.name} 失败: {e}")

            self.logger.info("所有子进程已关闭")

        except Exception as e:
            self.logger.error(f"关闭子进程失败: {e}")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="ORC MongoDB主服务 - 进程管理和监控",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 启动服务并运行监控模式（默认）
  python3 services/orc_mongodb_service/main.py

  # 运行监控模式
  python3 services/orc_mongodb_service/main.py --monitor

  # 显示服务状态
  python3 services/orc_mongodb_service/main.py --status

  # 指定配置文件
  python3 services/orc_mongodb_service/main.py --config configs/orc_mongodb_service/production.yaml

注意:
  - 主服务负责启动和管理子服务进程
  - 各子服务独立运行，拥有自己的日志文件
  - ORC数据处理由独立的子服务完成
        """
    )

    # 运行模式
    parser.add_argument("--monitor", action="store_true", default=True, help="运行监控模式（默认）")
    parser.add_argument("--status", action="store_true", help="显示服务状态")
    parser.add_argument("--start-services", action="store_true", help="仅启动子服务")

    # 配置文件参数
    parser.add_argument("--config", help="配置文件路径")

    args = parser.parse_args()

    try:
        # 设置环境变量
        if args.config:
            os.environ['USER_DF_CONFIG_FILE'] = args.config

        # 创建服务实例
        service = ORCMongoDBMainService(args.config)
        service.start_time = time.time()

        # 初始化服务
        await service.initialize()

        if args.status:
            # 显示状态
            status = await service.get_service_status()
            print(json.dumps(status, indent=2, ensure_ascii=False))
        elif args.start_services:
            # 仅启动子服务
            print("子服务已启动，主服务退出")
        else:
            # 运行监控模式（默认）
            await service.run_monitoring_loop()

        # 保持运行直到收到停止信号
        while service.is_running and not service.shutdown_requested:
            await asyncio.sleep(1)

    except KeyboardInterrupt:
        print("\n接收到中断信号")
    except Exception as e:
        print(f"服务运行失败: {e}", file=sys.stderr)
        sys.exit(1)
    finally:
        if 'service' in locals():
            await service.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
