#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控服务

监控ORC处理和MongoDB写入微服务的状态和性能
"""

import os
import sys
import asyncio
import json
import time
import subprocess
import psutil
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import aiohttp
import redis.asyncio as redis
from rich.console import Console
from rich.table import Table
from rich.live import Live
from rich.panel import Panel
from rich.layout import Layout
from rich.text import Text

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from shared.core import ConfigManager, Logger
from shared.database.mongodb import MongoDBPool


@dataclass
class ServiceStatus:
    """服务状态数据结构"""
    name: str
    url: str
    status: str
    uptime: float
    stats: Dict[str, Any]
    last_check: float
    error_message: Optional[str] = None


@dataclass
class ProcessStatus:
    """进程状态数据结构"""
    name: str
    pid: Optional[int]
    status: str
    cpu_percent: float
    memory_mb: float
    uptime: float
    command: str
    last_check: float
    error_message: Optional[str] = None


@dataclass
class DatabaseStats:
    """数据库统计信息"""
    connection_status: str
    total_collections: int
    total_documents: int
    database_size_mb: float
    index_size_mb: float
    last_check: float
    error_message: Optional[str] = None


@dataclass
class RedisStats:
    """Redis统计信息"""
    connection_status: str
    memory_used_mb: float
    total_keys: int
    stream_length: int
    connected_clients: int
    last_check: float
    error_message: Optional[str] = None


class MonitoringService:
    """监控服务"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        # 使用新的统一配置结构
        self.config = config_manager.get_config("monitoring_service", default={})
        self.global_config = config_manager.get_config("", default={})  # 获取根配置
        self.logger = Logger.get_logger(__name__)

        # 进程监控配置（不再使用HTTP服务监控）
        self.monitored_processes = {
            "mongodb_writer": {
                "name": "MongoDB写入程序",
                "process_name": "writer_program.py",
                "search_pattern": "mongodb_writer_service/writer_program.py"
            },
            "orc_processor": {
                "name": "ORC处理服务",
                "process_name": "orc_processor_service",
                "search_pattern": "orc_processor_service"
            }
        }

        # 监控状态
        self.process_status = {}
        self.database_stats = None
        self.redis_stats = None
        self.is_running = False
        self.console = Console()
        self.redis_client = None
        self.mongodb_pool = None

        # 监控统计
        self.monitor_stats = {
            "start_time": time.time(),
            "total_checks": 0,
            "failed_checks": 0,
            "alerts_sent": 0
        }
    
    async def initialize(self):
        """初始化监控服务"""
        try:
            self.logger.info("初始化监控服务...")

            # 初始化Redis连接
            redis_config = self.global_config.get("redis", {})
            self.redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                db=redis_config.get("db", 0),
                decode_responses=True
            )

            # 测试Redis连接
            await self.redis_client.ping()

            # 初始化MongoDB连接
            self.mongodb_pool = MongoDBPool(config_manager=self.config_manager)
            await self.mongodb_pool.initialize()

            self.is_running = True
            self.logger.info("监控服务初始化完成")

        except Exception as e:
            self.logger.error(f"监控服务初始化失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭监控服务"""
        try:
            self.logger.info("关闭监控服务...")
            self.is_running = False

            if self.redis_client:
                await self.redis_client.aclose()

            if self.mongodb_pool:
                self.mongodb_pool.close_all_connections()

            self.logger.info("监控服务已关闭")

        except Exception as e:
            self.logger.error(f"监控服务关闭失败: {e}")
    
    async def check_process_status(self, process_key: str, process_config: Dict[str, Any]) -> ProcessStatus:
        """检查单个进程的状态"""
        try:
            search_pattern = process_config["search_pattern"]

            if process_key == "orc_processor":
                # 对于ORC处理服务，使用ps aux | grep方式监控
                return await self._check_process_by_grep(process_config, search_pattern)
            else:
                # 对于其他进程，使用psutil监控
                return await self._check_process_by_psutil(process_config, search_pattern)

        except Exception as e:
            return ProcessStatus(
                name=process_config["name"],
                pid=None,
                status="error",
                cpu_percent=0.0,
                memory_mb=0.0,
                uptime=0.0,
                command="",
                last_check=time.time(),
                error_message=str(e)
            )

    async def _check_process_by_grep(self, process_config: Dict[str, Any], search_pattern: str) -> ProcessStatus:
        """使用grep方式检查进程"""
        try:
            # 执行ps aux | grep命令
            cmd = f"ps aux | grep '{search_pattern}' | grep -v grep"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0 and result.stdout.strip():
                # 解析ps输出
                lines = result.stdout.strip().split('\n')
                if lines:
                    # 取第一个匹配的进程
                    fields = lines[0].split()
                    if len(fields) >= 11:
                        pid = int(fields[1])
                        cpu_percent = float(fields[2])
                        memory_percent = float(fields[3])

                        # 获取进程详细信息
                        try:
                            process = psutil.Process(pid)
                            memory_mb = process.memory_info().rss / 1024 / 1024
                            uptime = time.time() - process.create_time()
                            command = ' '.join(process.cmdline())

                            return ProcessStatus(
                                name=process_config["name"],
                                pid=pid,
                                status="running",
                                cpu_percent=cpu_percent,
                                memory_mb=memory_mb,
                                uptime=uptime,
                                command=command,
                                last_check=time.time()
                            )
                        except psutil.NoSuchProcess:
                            return ProcessStatus(
                                name=process_config["name"],
                                pid=pid,
                                status="not_found",
                                cpu_percent=cpu_percent,
                                memory_mb=0.0,
                                uptime=0.0,
                                command="",
                                last_check=time.time(),
                                error_message="进程不存在"
                            )

            # 没有找到进程
            return ProcessStatus(
                name=process_config["name"],
                pid=None,
                status="stopped",
                cpu_percent=0.0,
                memory_mb=0.0,
                uptime=0.0,
                command="",
                last_check=time.time()
            )

        except Exception as e:
            return ProcessStatus(
                name=process_config["name"],
                pid=None,
                status="error",
                cpu_percent=0.0,
                memory_mb=0.0,
                uptime=0.0,
                command="",
                last_check=time.time(),
                error_message=str(e)
            )

    async def _check_process_by_psutil(self, process_config: Dict[str, Any], search_pattern: str) -> ProcessStatus:
        """使用psutil方式检查进程"""
        try:
            # 遍历所有进程查找匹配的进程
            for process in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_info', 'create_time']):
                try:
                    cmdline = ' '.join(process.info['cmdline'] or [])
                    if search_pattern in cmdline:
                        memory_mb = process.info['memory_info'].rss / 1024 / 1024
                        uptime = time.time() - process.info['create_time']

                        return ProcessStatus(
                            name=process_config["name"],
                            pid=process.info['pid'],
                            status="running",
                            cpu_percent=process.info['cpu_percent'] or 0.0,
                            memory_mb=memory_mb,
                            uptime=uptime,
                            command=cmdline,
                            last_check=time.time()
                        )
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # 没有找到进程
            return ProcessStatus(
                name=process_config["name"],
                pid=None,
                status="stopped",
                cpu_percent=0.0,
                memory_mb=0.0,
                uptime=0.0,
                command="",
                last_check=time.time()
            )

        except Exception as e:
            return ProcessStatus(
                name=process_config["name"],
                pid=None,
                status="error",
                cpu_percent=0.0,
                memory_mb=0.0,
                uptime=0.0,
                command="",
                last_check=time.time(),
                error_message=str(e)
            )
    
    async def get_redis_stats(self) -> RedisStats:
        """获取Redis统计信息"""
        try:
            # 获取Redis信息
            info = await self.redis_client.info()

            # 获取内存使用情况
            memory_used_mb = info.get('used_memory', 0) / 1024 / 1024

            # 获取连接客户端数
            connected_clients = info.get('connected_clients', 0)

            # 获取总键数
            total_keys = 0
            for db_key in info.keys():
                if db_key.startswith('db'):
                    db_info = info[db_key]
                    if 'keys' in db_info:
                        total_keys += db_info['keys']

            # 获取Stream长度
            stream_name = self.global_config.get("redis", {}).get("stream", {}).get("stream_name", "mongodb_write_stream")
            try:
                stream_length = await self.redis_client.xlen(stream_name)
            except:
                stream_length = 0

            return RedisStats(
                connection_status="connected",
                memory_used_mb=memory_used_mb,
                total_keys=total_keys,
                stream_length=stream_length,
                connected_clients=connected_clients,
                last_check=time.time()
            )

        except Exception as e:
            return RedisStats(
                connection_status="error",
                memory_used_mb=0.0,
                total_keys=0,
                stream_length=0,
                connected_clients=0,
                last_check=time.time(),
                error_message=str(e)
            )

    async def get_database_stats(self) -> DatabaseStats:
        """获取MongoDB统计信息"""
        try:
            # 获取数据库连接
            db = self.mongodb_pool.get_database()

            # 获取数据库统计信息
            db_stats = await db.command("dbStats")

            # 获取集合列表
            collections = await db.list_collection_names()
            total_collections = len(collections)

            # 计算总文档数
            total_documents = 0
            for collection_name in collections:
                try:
                    collection = db[collection_name]
                    count = await collection.count_documents({})
                    total_documents += count
                except:
                    continue

            # 数据库大小（字节转MB）
            database_size_mb = db_stats.get('dataSize', 0) / 1024 / 1024
            index_size_mb = db_stats.get('indexSize', 0) / 1024 / 1024

            return DatabaseStats(
                connection_status="connected",
                total_collections=total_collections,
                total_documents=total_documents,
                database_size_mb=database_size_mb,
                index_size_mb=index_size_mb,
                last_check=time.time()
            )

        except Exception as e:
            return DatabaseStats(
                connection_status="error",
                total_collections=0,
                total_documents=0,
                database_size_mb=0.0,
                index_size_mb=0.0,
                last_check=time.time(),
                error_message=str(e)
            )
    
    async def get_queue_status(self) -> Dict[str, Any]:
        """获取Redis Stream队列状态"""
        try:
            stream_name = self.global_config.get("redis", {}).get("stream", {}).get("stream_name", "mongodb_write_stream")

            # 获取Stream长度
            stream_length = await self.redis_client.xlen(stream_name)

            # 获取消费者组信息
            try:
                groups = await self.redis_client.xinfo_groups(stream_name)
                group_info = groups[0] if groups else {}
            except:
                group_info = {}

            return {
                "stream_name": stream_name,
                "stream_length": stream_length,
                "consumer_groups": len(groups) if 'groups' in locals() else 0,
                "pending_messages": group_info.get("pending", 0),
                "status": "healthy" if stream_length >= 0 else "error"
            }

        except Exception as e:
            return {
                "stream_name": "unknown",
                "stream_length": -1,
                "consumer_groups": 0,
                "pending_messages": 0,
                "status": "error",
                "error": str(e)
            }
    
    async def collect_all_stats(self) -> Dict[str, Any]:
        """收集所有统计信息"""
        all_stats = {
            "timestamp": time.time(),
            "processes": {},
            "queue": {},
            "redis": {},
            "database": {},
            "monitor": self.monitor_stats
        }

        # 检查所有进程
        for process_key, process_config in self.monitored_processes.items():
            # 进程状态检查
            process_status = await self.check_process_status(process_key, process_config)
            self.process_status[process_key] = process_status

            all_stats["processes"][process_key] = asdict(process_status)

            self.monitor_stats["total_checks"] += 1
            if process_status.status not in ["running"]:
                self.monitor_stats["failed_checks"] += 1

        # 队列状态
        all_stats["queue"] = await self.get_queue_status()

        # Redis统计
        redis_stats = await self.get_redis_stats()
        self.redis_stats = redis_stats
        all_stats["redis"] = asdict(redis_stats)

        # 数据库统计
        database_stats = await self.get_database_stats()
        self.database_stats = database_stats
        all_stats["database"] = asdict(database_stats)

        return all_stats
    
    def create_monitoring_display(self, stats: Dict[str, Any]) -> Layout:
        """创建监控显示界面"""
        layout = Layout()
        
        # 创建主要布局
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body"),
            Layout(name="footer", size=3)
        )
        
        # 头部信息
        header_text = Text("User-DF 微服务监控面板", style="bold blue")
        header_text.append(f" - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", style="dim")
        layout["header"].update(Panel(header_text, title="监控状态"))
        
        # 主体分为左右两部分
        layout["body"].split_row(
            Layout(name="services", ratio=2),
            Layout(name="details", ratio=1)
        )
        
        # 进程状态表格
        processes_table = Table(title="进程状态")
        processes_table.add_column("进程名称", style="cyan")
        processes_table.add_column("状态", style="green")
        processes_table.add_column("PID", style="yellow")
        processes_table.add_column("CPU%", style="blue")
        processes_table.add_column("内存(MB)", style="magenta")
        processes_table.add_column("运行时间", style="yellow")
        processes_table.add_column("错误信息", style="red")

        for process_key, process_data in stats["processes"].items():
            status_color = "green" if process_data["status"] == "running" else "red"
            uptime_str = self._format_uptime(process_data["uptime"])
            error_msg = process_data.get("error_message", "") or ""

            processes_table.add_row(
                process_data["name"],
                f"[{status_color}]{process_data['status']}[/{status_color}]",
                str(process_data["pid"]) if process_data["pid"] else "N/A",
                f"{process_data['cpu_percent']:.1f}",
                f"{process_data['memory_mb']:.1f}",
                uptime_str,
                error_msg[:20] + "..." if len(error_msg) > 20 else error_msg
            )

        layout["services"].update(Panel(processes_table, title="进程状态"))
        
        # 详细信息
        details_text = []

        # 队列信息
        queue_info = stats["queue"]
        details_text.append(f"Stream状态: {queue_info.get('status', 'unknown')}")
        details_text.append(f"Stream长度: {queue_info.get('stream_length', 0)}")
        details_text.append(f"待处理消息: {queue_info.get('pending_messages', 0)}")
        details_text.append("")

        # Redis信息
        redis_info = stats["redis"]
        details_text.append(f"Redis状态: {redis_info.get('connection_status', 'unknown')}")
        details_text.append(f"Redis内存: {redis_info.get('memory_used_mb', 0):.1f}MB")
        details_text.append(f"Redis键数: {redis_info.get('total_keys', 0)}")
        details_text.append("")

        # 数据库信息
        db_info = stats["database"]
        details_text.append(f"MongoDB状态: {db_info.get('connection_status', 'unknown')}")
        details_text.append(f"集合数: {db_info.get('total_collections', 0)}")
        details_text.append(f"文档数: {db_info.get('total_documents', 0):,}")
        details_text.append(f"数据大小: {db_info.get('database_size_mb', 0):.1f}MB")
        details_text.append("")

        # 监控统计
        details_text.append(f"监控检查: {self.monitor_stats['total_checks']}")
        details_text.append(f"检查失败: {self.monitor_stats['failed_checks']}")

        details_panel = Panel("\n".join(details_text), title="系统信息")
        layout["details"].update(details_panel)
        
        # 底部信息
        footer_text = f"监控运行时间: {self._format_uptime(time.time() - self.monitor_stats['start_time'])}"
        layout["footer"].update(Panel(footer_text, style="dim"))
        
        return layout
    
    def _format_uptime(self, seconds: float) -> str:
        """格式化运行时间"""
        if seconds < 60:
            return f"{int(seconds)}秒"
        elif seconds < 3600:
            return f"{int(seconds // 60)}分{int(seconds % 60)}秒"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}时{minutes}分"
    
    async def run_monitoring_loop(self):
        """运行监控循环"""
        check_interval = self.config.get("monitoring", {}).get("check_interval", 5)
        
        with Live(console=self.console, refresh_per_second=1) as live:
            while self.is_running:
                try:
                    # 收集统计信息
                    stats = await self.collect_all_stats()
                    
                    # 更新显示
                    display = self.create_monitoring_display(stats)
                    live.update(display)
                    
                    # 等待下次检查
                    await asyncio.sleep(check_interval)
                    
                except KeyboardInterrupt:
                    self.logger.info("接收到中断信号，停止监控")
                    break
                except Exception as e:
                    self.logger.error(f"监控循环错误: {e}")
                    await asyncio.sleep(1)
    
    async def run_once(self) -> Dict[str, Any]:
        """运行一次监控检查"""
        return await self.collect_all_stats()
    
    def print_stats_summary(self, stats: Dict[str, Any]):
        """打印统计摘要"""
        self.console.print("\n=== 系统监控状态摘要 ===", style="bold blue")

        # 进程状态
        self.console.print("\n[bold cyan]进程状态[/bold cyan]:")
        for process_key, process_data in stats["processes"].items():
            status_style = "green" if process_data["status"] == "running" else "red"

            self.console.print(f"\n[cyan]{process_data['name']}[/cyan]:")
            self.console.print(f"  状态: [{status_style}]{process_data['status']}[/{status_style}]")
            self.console.print(f"  PID: {process_data['pid'] or 'N/A'}")
            self.console.print(f"  CPU: {process_data['cpu_percent']:.1f}%")
            self.console.print(f"  内存: {process_data['memory_mb']:.1f}MB")
            self.console.print(f"  运行时间: {self._format_uptime(process_data['uptime'])}")

            if process_data.get("error_message"):
                self.console.print(f"  错误: [red]{process_data['error_message']}[/red]")

        # Redis状态
        redis_info = stats["redis"]
        redis_status_style = "green" if redis_info.get('connection_status') == 'connected' else "red"
        self.console.print(f"\n[bold cyan]Redis状态[/bold cyan]:")
        self.console.print(f"  连接状态: [{redis_status_style}]{redis_info.get('connection_status', 'unknown')}[/{redis_status_style}]")
        self.console.print(f"  内存使用: {redis_info.get('memory_used_mb', 0):.1f}MB")
        self.console.print(f"  总键数: {redis_info.get('total_keys', 0)}")
        self.console.print(f"  Stream长度: {redis_info.get('stream_length', 0)}")
        self.console.print(f"  连接客户端: {redis_info.get('connected_clients', 0)}")

        # 数据库状态
        db_info = stats["database"]
        db_status_style = "green" if db_info.get('connection_status') == 'connected' else "red"
        self.console.print(f"\n[bold cyan]MongoDB状态[/bold cyan]:")
        self.console.print(f"  连接状态: [{db_status_style}]{db_info.get('connection_status', 'unknown')}[/{db_status_style}]")
        self.console.print(f"  集合数: {db_info.get('total_collections', 0)}")
        self.console.print(f"  文档数: {db_info.get('total_documents', 0):,}")
        self.console.print(f"  数据大小: {db_info.get('database_size_mb', 0):.1f}MB")
        self.console.print(f"  索引大小: {db_info.get('index_size_mb', 0):.1f}MB")

        # 队列状态
        queue_info = stats["queue"]
        self.console.print(f"\n[bold cyan]消息队列[/bold cyan]:")
        self.console.print(f"  Stream状态: {queue_info.get('status', 'unknown')}")
        self.console.print(f"  Stream长度: {queue_info.get('stream_length', 0)}")
        self.console.print(f"  待处理消息: {queue_info.get('pending_messages', 0)}")
        self.console.print(f"  消费者组: {queue_info.get('consumer_groups', 0)}")


async def create_monitoring_service():
    """创建监控服务实例"""
    config_manager = ConfigManager()
    service = MonitoringService(config_manager)
    await service.initialize()
    return service


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="微服务监控服务")
    parser.add_argument("--mode", choices=["live", "once"], default="live", 
                       help="监控模式: live=实时监控, once=单次检查")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    # 设置配置文件路径
    if args.config:
        os.environ['USER_DF_CONFIG_FILE'] = args.config
    
    async def main():
        service = await create_monitoring_service()
        
        try:
            if args.mode == "live":
                await service.run_monitoring_loop()
            else:
                stats = await service.run_once()
                service.print_stats_summary(stats)
        finally:
            await service.shutdown()
    
    asyncio.run(main())
